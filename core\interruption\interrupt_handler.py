"""
Interrupt Handler

Implements a streamlined interrupt system with the exact 7-step flow:
1. TTS Playback
2. Interrupt Detection
3. TTS Pause
4. Acknowledgment
5. TTS Resume
6. Action Reversibility Check
7. Conditional Queuing

"""

import asyncio
import time
from typing import Dict, Any, Optional

try:
    import sounddevice as sd
    import numpy as np
    SOUNDDEVICE_AVAILABLE = True
except ImportError:
    SOUNDDEVICE_AVAILABLE = False

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False

try:
    import pyaudio
    _pyaudio_available = True
except ImportError:
    _pyaudio_available = False

from core.logging.logger_config import get_module_logger
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from core.interruption.action_reversibility import ActionReversibilityDetector
from core.config.interrupt_config import get_interrupt_config



class InterruptHandler:
    """
    Interrupt handler that implements the exact 7-step flow.
    Focuses on pause/resume functionality with conditional queuing.
    """
    
    def __init__(self, session_id: str, memory_manager, interrupt_config=None):
        self.session_id = session_id
        self.memory_manager = memory_manager
        self.interrupt_config = interrupt_config or get_interrupt_config()
        self.logger = get_module_logger("InterruptHandler", session_id=session_id)
        
        # TTS playback state
        self.is_playing = False
        self.is_paused = False
        self.current_audio_path = None
        self.playback_start_time = None
        self.pause_time = None
        self.total_pause_duration = 0
        
        # Interrupt state
        self.interrupt_detected = False
        self.user_interrupt_input = None
        
        # Action reversibility detector
        self.reversibility_detector = ActionReversibilityDetector()



        # Initialize pygame if available (fallback)
        if PYGAME_AVAILABLE:
            try:
                pygame.mixer.init()
                self.logger.info("Pygame mixer initialized for TTS playback")
            except Exception as e:
                self.logger.warning(f"Failed to initialize pygame mixer: {e}")







    async def _check_for_interrupt(self) -> bool:
        """
        Step 2: Real-time interrupt detection with microphone monitoring

        This method should be called frequently during TTS playback to detect interrupts.
        """
        try:
            # First check memory for interrupt signal (for testing/external triggers)
            interrupt_context = await self.memory_manager.get_interrupt_context()
            if interrupt_context.get("detected") and not interrupt_context.get("handled"):
                self.user_interrupt_input = interrupt_context.get("user_input_queued", "User interrupted")

                # Mark as handled
                await self.memory_manager.set_interrupt_context(
                    detected=True,
                    handled=True,
                    user_input_queued=self.user_interrupt_input
                )

                return True

            # Real-time microphone monitoring for interrupts
            return await self._check_microphone_for_speech()

        except Exception as e:
            self.logger.error(f"Error checking for interrupt: {e}")
            return False

    async def _check_microphone_for_speech(self) -> bool:
        """
        Check microphone for speech activity using VAD (based on proven TTS interrupt monitor).
        Returns True if speech is detected (indicating an interrupt).
        """
        try:
            # Check if sounddevice is available
            if not SOUNDDEVICE_AVAILABLE:
                return False

            # Get microphone device from memory, use default if not set
            device_index = await self.memory_manager.get("microphone_device_index")

            # Use default microphone if none configured
            if device_index is None:
                device_index = None  # sounddevice will use default device
                self.logger.info("Using default microphone device for interrupt detection")

            # Use proven parameters from the old system
            sample_rate = 16000
            chunk_duration = 0.3  # 300ms chunks (proven to work)
            channels = 1

            # Audio capture with error handling
            try:
                audio = sd.rec(int(chunk_duration * sample_rate),
                             samplerate=sample_rate,
                             channels=channels,
                             dtype='int16',
                             device=device_index)
                sd.wait()  # Wait for recording to complete
                audio_bytes = audio.tobytes()

                # Calculate audio energy (same as old system)
                audio_samples = np.frombuffer(audio_bytes, dtype=np.int16)
                audio_squared = audio_samples.astype(np.float64) ** 2
                mean_squared = np.mean(audio_squared)

                if mean_squared > 0:
                    audio_rms = np.sqrt(mean_squared)
                else:
                    audio_rms = 0.0

                audio_peak = np.max(np.abs(audio_samples))

                # Enhanced TTS feedback detection to prevent AI voice capture
                peak_to_rms_ratio = audio_peak / (audio_rms + 1e-6)

                # Skip if likely TTS feedback (more aggressive filtering)
                if peak_to_rms_ratio > 10.0 and audio_rms > 3000:  # Lowered thresholds for better filtering
                    self.logger.debug(f"Skipping - likely TTS feedback (peak/RMS: {peak_to_rms_ratio:.2f}, RMS: {audio_rms:.1f})")
                    return False

                # Additional check: Skip very high energy levels (likely AI voice)
                if audio_rms > 15000:  # Very high energy is likely AI voice
                    self.logger.debug(f"Skipping - very high energy likely AI voice (RMS: {audio_rms:.1f})")
                    return False

                # Skip if energy is too consistent (AI voice tends to be more consistent)
                audio_variance = np.var(audio_samples.astype(np.float64))
                if audio_variance < 1000000 and audio_rms > 5000:  # Low variance + high energy = likely AI
                    self.logger.debug(f"Skipping - consistent energy pattern likely AI voice (variance: {audio_variance:.0f})")
                    return False

                # Use AudioProcessor for VAD (same as old system)
                try:
                    from utils.audio_utils import AudioProcessor

                    # Cache interrupt config to avoid repeated calls
                    if not hasattr(self, '_cached_interrupt_config'):
                        self._cached_interrupt_config = await get_interrupt_config()

                    interrupt_config = self._cached_interrupt_config
                    vad_threshold = interrupt_config.global_settings.vad_threshold if interrupt_config else 0.1

                    ap = AudioProcessor(interrupt_config)
                    vad_result = ap.detect_voice_activity(audio_bytes, threshold=vad_threshold)

                    if vad_result.status.value == 'success' and vad_result.outputs.get('has_voice'):
                        energy = vad_result.outputs.get('energy', 0)
                        self.logger.info(f"🎤 Voice activity detected during TTS! Energy: {energy:.0f}")

                        # Skip very high energy (likely TTS feedback)
                        if energy > 50000000:
                            self.logger.debug("Very high energy - likely TTS feedback, skipping")
                            return False

                        # Quick confirmation (simplified version from old system)
                        await asyncio.sleep(0.1)

                        # Second check for confirmation
                        quick_audio = sd.rec(int(0.2 * sample_rate),
                                           samplerate=sample_rate,
                                           channels=channels,
                                           dtype='int16',
                                           device=device_index)
                        sd.wait()
                        quick_bytes = quick_audio.tobytes()
                        quick_result = ap.detect_voice_activity(quick_bytes, threshold=vad_threshold)

                        if quick_result.status.value == 'success' and quick_result.outputs.get('has_voice'):
                            self.logger.info(f"🎯 REAL USER VOICE DETECTED! RMS: {audio_rms:.1f}, Peak/RMS: {peak_to_rms_ratio:.2f}")

                            # Capture and transcribe the actual user speech
                            user_speech = await self._capture_user_speech_during_interrupt(quick_audio)
                            self.user_interrupt_input = user_speech or "User interrupted during TTS playback"

                            self.logger.info(f"📝 Captured user input: '{self.user_interrupt_input}'")
                            return True
                        else:
                            self.logger.debug("Confirmation failed - no sustained voice")
                            return False

                    return False

                except ImportError as import_error:
                    self.logger.debug(f"AudioProcessor not available: {import_error}")
                    # Fallback to simple energy detection
                    if audio_rms > 1000:  # Simple threshold
                        self.logger.info(f"🎤 Simple energy detection triggered! RMS: {audio_rms:.1f}")

                        # Capture and transcribe the actual user speech (like your old system!)
                        user_speech = await self._capture_and_transcribe_interrupt_audio()
                        self.user_interrupt_input = user_speech or "User interrupted during TTS playback"

                        self.logger.info(f"📝 Captured user input: '{self.user_interrupt_input}'")
                        return True
                    return False

            except Exception as audio_error:
                self.logger.debug(f"Audio capture failed: {audio_error}")
                return False

        except Exception as e:
            self.logger.error(f"Error in microphone speech check: {e}")
            return False

    async def _simple_microphone_check(self) -> bool:
        """
        Simple microphone check using energy-based detection.
        Returns True if speech is detected (indicating an interrupt).
        """
        try:
            # Check if sounddevice is available
            if not SOUNDDEVICE_AVAILABLE:
                return False

            # Get microphone device from memory
            device_index = await self.memory_manager.get("microphone_device_index")

            if device_index is None:
                return False

            # Simple audio capture and energy detection
            sample_rate = 16000
            chunk_duration = 0.3  # 300ms

            # Capture audio
            audio = sd.rec(int(chunk_duration * sample_rate),
                         samplerate=sample_rate,
                         channels=1,
                         dtype='int16',
                         device=device_index)
            sd.wait()

            # Calculate RMS energy
            audio_samples = np.frombuffer(audio.tobytes(), dtype=np.int16)
            audio_squared = audio_samples.astype(np.float64) ** 2
            mean_squared = np.mean(audio_squared)

            if mean_squared > 0:
                audio_rms = np.sqrt(mean_squared)
            else:
                audio_rms = 0.0

            # Simple energy threshold for interrupt detection (lowered for faster detection)
            energy_threshold = 800  # Lowered from 2000 for faster interrupt detection

            if audio_rms > energy_threshold:
                self.logger.info(f"🎤 Simple interrupt detected! RMS Energy: {audio_rms:.1f}")

                # Capture and transcribe the actual user speech (like your old system!)
                user_speech = await self._capture_and_transcribe_interrupt_audio()
                self.user_interrupt_input = user_speech or "User interrupted during TTS playback"

                self.logger.info(f"📝 Captured user input: '{self.user_interrupt_input}'")
                return True

            # Log energy levels for debugging (every 10th check)
            if hasattr(self, '_energy_check_count'):
                self._energy_check_count += 1
            else:
                self._energy_check_count = 1

            if self._energy_check_count % 10 == 0:
                self.logger.debug(f"🎤 Monitoring... RMS Energy: {audio_rms:.1f} (threshold: {energy_threshold})")

            return False

        except Exception as e:
            self.logger.debug(f"Simple microphone check failed: {e}")
            return False

    async def handle_tts_with_real_concurrent_monitoring(self, audio_path: str, workflow_context: Dict[str, Any]) -> StateOutput:
        """
        Enhanced version that uses TTSPlaybackController for real concurrent TTS playback and interrupt monitoring.

        This implements true concurrent monitoring:
        1. Starts real TTS audio playback
        2. Simultaneously monitors microphone for interrupts
        3. Pauses TTS immediately when interrupt detected
        4. Handles acknowledgment and resume
        """
        try:
            self.logger.info(
                "Starting TTS with real concurrent interrupt monitoring",
                action="handle_tts_with_real_concurrent_monitoring",
                input_data={"audio_path": audio_path}
            )

            # Step 1: Start real TTS playback with interrupt detection
            # Use the working audio playback method from filler TTS
            await self._play_tts_with_concurrent_monitoring(audio_path, workflow_context)

            # Check if interrupt occurred during playback
            interrupt_occurred = self.interrupt_detected

            if interrupt_occurred:
                self.logger.info("Real-time interrupt detected during TTS playback")

                # Steps 6-7: Check action reversibility and handle conditional queuing
                reversible, level = self._check_action_reversibility(workflow_context)

                if reversible:
                    return StateOutput(
                        status=StatusType.SUCCESS,
                        message="TTS interrupted and user input queued for reversible action",
                        code=StatusCode.OK,
                        outputs={
                            "interrupt_occurred": True,
                            "action_reversible": True,
                            "user_input_queued": True,
                            "reversibility_level": level
                        },
                        meta={"interrupt_flow": "completed"}
                    )
                else:
                    return StateOutput(
                        status=StatusType.SUCCESS,
                        message="TTS interrupted but user input not queued for irreversible action",
                        code=StatusCode.OK,
                        outputs={
                            "interrupt_occurred": True,
                            "action_reversible": False,
                            "user_input_queued": False,
                            "reversibility_level": level
                        },
                        meta={"interrupt_flow": "completed"}
                    )
            else:
                # TTS completed without interruption
                return StateOutput(
                    status=StatusType.SUCCESS,
                    message="TTS playback completed without interruption",
                    code=StatusCode.OK,
                    outputs={
                        "interrupt_occurred": False,
                        "action_reversible": "unknown",
                        "user_input_queued": False
                    },
                    meta={"playback": "completed"}
                )

        except Exception as e:
            self.logger.error(f"Error in real concurrent monitoring: {e}")
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Concurrent monitoring error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={"interrupt_occurred": False},
                meta={"error": str(e)}
            )



    async def _play_tts_with_concurrent_monitoring(self, audio_path: str, workflow_context: Dict[str, Any]):
        """
        Play TTS audio with concurrent interrupt monitoring using the proven TTSPlaybackController.
        """
        try:
            self.logger.info(f"🎵 Starting TTS playback with real interrupt monitoring: {audio_path}")

            # Store TTS playback state
            await self.memory_manager.set_tts_playback_state(
                audio_path=audio_path,
                status="playing",
                playback_position=0.0
            )

            # Simple approach: Use pygame for audio + our own microphone monitoring
            self.logger.info("🎵 Starting simple TTS playback with direct microphone monitoring")

            # Initialize pygame for audio playback
            if PYGAME_AVAILABLE:
                pygame.mixer.quit()  # Quit any existing mixer
                pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
                pygame.mixer.music.set_volume(1.0)

                # Load and start audio
                pygame.mixer.music.load(audio_path)
                pygame.mixer.music.play()

                self.logger.info(f"🎵 Pygame audio started - Volume: {pygame.mixer.music.get_volume()}")
                self.logger.info("� Starting direct microphone monitoring...")

                # Monitor for interrupts while audio is playing
                max_wait_time = 30
                wait_start = time.time()

                while pygame.mixer.music.get_busy():
                    # Check for interrupts every 300ms (same as old system)
                    interrupt_detected = await self._simple_microphone_check()

                    if interrupt_detected:
                        self.interrupt_detected = True

                        # Get exact playback position using pygame (your old working method!)
                        playback_position = pygame.mixer.music.get_pos() / 1000.0  # Convert ms to seconds

                        # Stop pygame immediately to capture exact position
                        pygame.mixer.music.stop()
                        self.logger.info(f"🎯 Interrupt detected at {playback_position:.2f}s - pygame stopped!")

                        # Handle the interrupt (this plays acknowledgment)
                        await self._handle_interrupt_during_playback(workflow_context)

                        # Resume original TTS playback from exact position (your old working method!)
                        self.logger.info(f"▶️ Resuming original TTS from {playback_position:.2f} seconds...")
                        pygame.mixer.music.load(audio_path)
                        pygame.mixer.music.play(start=playback_position)  # ← This is your old working approach!

                        self.logger.info("✅ Original TTS playback resumed from exact position!")

                        # Reset interrupt flag for potential future interrupts
                        self.interrupt_detected = False

                    await asyncio.sleep(0.3)  # Check every 300ms

                    # Safety timeout
                    if time.time() - wait_start > max_wait_time:
                        self.logger.warning("⚠️ TTS playback timeout - forcing completion")
                        break

                if not self.interrupt_detected:
                    self.logger.info("✅ TTS playback completed without interruption")

            else:
                # Fallback if pygame not available
                self.logger.warning("Pygame not available - using fallback playback")
                await self._fallback_tts_playback(audio_path)

            # Update memory state to completed
            await self.memory_manager.set_tts_playback_state(
                audio_path=audio_path,
                status="completed",
                playback_position=0.0
            )

        except Exception as e:
            self.logger.error(f"Error in TTS playback with monitoring: {e}")
            # Fallback to simple playback
            await self._fallback_tts_playback(audio_path)

    async def _fallback_tts_playback(self, audio_path: str):
        """Fallback TTS playback using multiple methods."""
        try:
            # Try pygame first
            if PYGAME_AVAILABLE:
                try:
                    pygame.mixer.init()
                    pygame.mixer.music.load(audio_path)
                    pygame.mixer.music.play()

                    self.logger.info(f"🎵 Using pygame fallback for TTS: {audio_path}")

                    # Wait for playback to complete
                    while pygame.mixer.music.get_busy():
                        await asyncio.sleep(0.1)

                    self.logger.info("✅ Pygame fallback TTS playback completed")
                    return

                except Exception as pygame_error:
                    self.logger.warning(f"Pygame fallback failed: {pygame_error}")

            # Try playsound as last resort
            try:
                from playsound import playsound
                self.logger.info(f"🎵 Using playsound fallback for TTS: {audio_path}")

                # Run playsound in a thread to avoid blocking
                await asyncio.to_thread(playsound, audio_path)

                self.logger.info("✅ Playsound fallback TTS playback completed")

            except Exception as playsound_error:
                self.logger.error(f"Playsound fallback failed: {playsound_error}")
                self.logger.info(f"📁 TTS audio file available at: {audio_path}")
                self.logger.info("🔊 You can manually play the audio file to hear the TTS output")

        except Exception as e:
            self.logger.error(f"❌ All TTS playback methods failed: {e}")
            self.logger.info(f"📁 TTS audio file available at: {audio_path}")

    async def _handle_interrupt_during_playback(self, workflow_context: Dict[str, Any]):
        """Handle interrupt that occurred during TTS playback."""
        try:
            self.logger.info("🎤 Handling interrupt during TTS playback")

            # Step 4: Play acknowledgment message
            await self._play_acknowledgment_message()

            # Steps 6-7: Check action reversibility and handle conditional queuing
            reversible, _ = self._check_action_reversibility(workflow_context)

            if reversible:
                # Queue the user input for processing
                await self._handle_conditional_queuing(reversible)
                self.logger.info("✅ User input queued for reversible action")
            else:
                self.logger.info("ℹ️ User input not queued for irreversible action")

        except Exception as e:
            self.logger.error(f"Error handling interrupt during playback: {e}")

    def _get_current_playback_position(self) -> float:
        """Get current playback position in seconds"""
        if not self.playback_start_time:
            return 0.0
        
        elapsed = time.time() - self.playback_start_time - self.total_pause_duration
        return max(0.0, elapsed)

    async def _pause_tts_playback(self):
        """Step 3: Pause TTS at current position"""
        if PYGAME_AVAILABLE and pygame.mixer.music.get_busy():
            pygame.mixer.music.pause()
            self.is_paused = True
            self.pause_time = time.time()
            
            self.logger.info("TTS playback paused due to interrupt")

    async def _play_acknowledgment_message(self):
        """Step 4: Play brief acknowledgment message from workflow config"""
        try:
            # Get acknowledgment message from workflow configuration
            acknowledgment_text = await self._get_workflow_interrupt_message()

            self.logger.info(f"🔊 Playing workflow acknowledgment: {acknowledgment_text}")

            # Generate TTS for acknowledgment
            try:
                from agents.tts.tts_agent import TTSAgent

                # Create TTS agent
                tts_agent = TTSAgent(session_id=self.session_id)

                # Generate acknowledgment audio
                tts_result = await tts_agent.text_to_speech(acknowledgment_text)

                if tts_result.status == StatusType.SUCCESS:
                    ack_audio_path = tts_result.outputs.get("audio_path")

                    if ack_audio_path and PYGAME_AVAILABLE:
                        # Play acknowledgment with pygame
                        pygame.mixer.music.load(ack_audio_path)
                        pygame.mixer.music.play()

                        self.logger.info("🔊 Playing acknowledgment audio...")

                        # Wait for acknowledgment to complete
                        while pygame.mixer.music.get_busy():
                            await asyncio.sleep(0.1)

                        self.logger.info("✅ Acknowledgment completed")
                    else:
                        self.logger.warning("No acknowledgment audio path or pygame unavailable")
                        await asyncio.sleep(1.0)  # Fallback pause
                else:
                    self.logger.error(f"Failed to generate acknowledgment TTS: {tts_result.message}")
                    await asyncio.sleep(1.0)  # Fallback pause

            except Exception as tts_error:
                self.logger.error(f"Error generating acknowledgment TTS: {tts_error}")
                # Fallback: just pause briefly
                await asyncio.sleep(1.0)

        except Exception as e:
            self.logger.error(f"Error playing acknowledgment: {e}")

    async def _get_workflow_interrupt_message(self) -> str:
        """Get the interrupt message from the current workflow state configuration."""
        try:
            # Try multiple ways to get current state
            current_state = await self.memory_manager.get("current_state")

            if not current_state:
                # Try alternative state keys
                current_state = await self.memory_manager.get("workflow_state")

            if not current_state:
                # Try to get from workflow context - check if we're in Inquiry state
                # Look for Inquiry-specific memory keys to determine current state
                inquiry_text = await self.memory_manager.get(f"{self.session_id}_Inquiry_clean_text")
                if inquiry_text:
                    current_state = "Inquiry"
                    self.logger.info(f"Detected current state from memory context: {current_state}")

            if not current_state:
                # Default to "Greeting" if we can't determine the state
                current_state = "Greeting"
                self.logger.warning(f"Could not determine current state, defaulting to: {current_state}")

            self.logger.info(f"🔍 Getting interrupt message for state: {current_state}")

            # Get workflow configuration
            workflow_name = await self.memory_manager.get("workflow_name")

            if workflow_name:
                import json
                import os

                workflow_path = os.path.join("workflows", workflow_name)
                self.logger.info(f"📁 Loading workflow config from: {workflow_path}")

                if os.path.exists(workflow_path):
                    with open(workflow_path, 'r') as f:
                        workflow_config = json.load(f)

                    # Navigate to the current state's interrupt config
                    states = workflow_config.get("workflow", {}).get("states", {})
                    state_config = states.get(current_state, {})
                    interrupt_config = state_config.get("interrupt_config", {})
                    interrupt_message = interrupt_config.get("interrupt_message")

                    if interrupt_message:
                        self.logger.info(f"📋 Using workflow interrupt message for state '{current_state}': {interrupt_message}")
                        return interrupt_message
                    else:
                        self.logger.warning(f"No interrupt_message found for state '{current_state}'")
                else:
                    self.logger.error(f"Workflow file not found: {workflow_path}")
            else:
                self.logger.warning("No workflow_name found in memory")

            # Fallback message
            fallback_message = "I hear you, let me finish first"
            self.logger.info(f"🔄 Using fallback interrupt message: {fallback_message}")
            return fallback_message

        except Exception as e:
            self.logger.error(f"Error getting workflow interrupt message: {e}")
            return "I hear you, let me finish first"  # Default fallback

    async def _capture_user_speech_during_interrupt(self, initial_audio: np.ndarray) -> Optional[str]:
        """
        Capture and transcribe user speech during interrupt.

        Args:
            initial_audio: Initial audio that triggered the interrupt

        Returns:
            str: Transcribed user speech, or None if transcription fails
        """
        try:
            self.logger.info("🎤 Capturing user speech for transcription...")

            # Record additional audio to get complete user utterance
            additional_duration = 2.0  # Record 2 more seconds
            sample_rate = 16000

            if _pyaudio_available:
                try:
                    # Initialize PyAudio for additional recording
                    p = pyaudio.PyAudio()
                    stream = p.open(
                        format=pyaudio.paInt16,
                        channels=1,
                        rate=sample_rate,
                        input=True,
                        frames_per_buffer=1024
                    )

                    # Record additional audio
                    additional_frames = []
                    for _ in range(int(sample_rate * additional_duration / 1024)):
                        data = stream.read(1024)
                        additional_frames.append(np.frombuffer(data, dtype=np.int16))

                    stream.stop_stream()
                    stream.close()
                    p.terminate()

                    # Combine initial and additional audio
                    additional_audio = np.concatenate(additional_frames)
                    complete_audio = np.concatenate([initial_audio, additional_audio])

                except Exception as audio_error:
                    self.logger.warning(f"Failed to capture additional audio: {audio_error}")
                    complete_audio = initial_audio
            else:
                complete_audio = initial_audio

            # Transcribe the captured audio using STT
            transcribed_text = await self._transcribe_audio(complete_audio, sample_rate)

            if transcribed_text and transcribed_text.strip():
                self.logger.info(f"✅ Successfully transcribed: '{transcribed_text}'")
                return transcribed_text.strip()
            else:
                self.logger.warning("⚠️ Transcription returned empty result")
                return None

        except Exception as e:
            self.logger.error(f"Error capturing user speech: {e}")
            return None

    async def _transcribe_audio(self, audio_data: np.ndarray, sample_rate: int) -> Optional[str]:
        """
        Transcribe audio data to text using STT agent.

        Args:
            audio_data: Audio data as numpy array
            sample_rate: Sample rate of the audio

        Returns:
            str: Transcribed text, or None if transcription fails
        """
        try:
            # Save audio to temporary file
            import tempfile
            import wave

            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_path = temp_file.name

                # Write audio data to WAV file
                with wave.open(temp_path, 'wb') as wav_file:
                    wav_file.setnchannels(1)  # Mono
                    wav_file.setsampwidth(2)  # 16-bit
                    wav_file.setframerate(sample_rate)
                    wav_file.writeframes(audio_data.tobytes())

            # Use STT agent to transcribe
            try:
                from agents.stt.stt_agent import STTAgent

                stt_agent = STTAgent(session_id=self.session_id)
                stt_result = await stt_agent.speech_to_text(temp_path)

                if stt_result.status == StatusType.SUCCESS:
                    transcribed_text = stt_result.outputs.get("text", "").strip()
                    self.logger.info(f"🎯 STT transcription: '{transcribed_text}'")
                    return transcribed_text
                else:
                    self.logger.error(f"STT failed: {stt_result.message}")
                    return None

            except Exception as stt_error:
                self.logger.error(f"STT agent error: {stt_error}")
                return None
            finally:
                # Clean up temporary file
                try:
                    import os
                    os.unlink(temp_path)
                except:
                    pass

        except Exception as e:
            self.logger.error(f"Error in audio transcription: {e}")
            return None

    async def _capture_and_transcribe_interrupt_audio(self, duration_sec: float = 3.0, sample_rate: int = 16000) -> str:
        """
        Capture real audio from microphone during interrupt and transcribe it to text.
        This is based on your old working approach from tts_interrupt_monitor.py!

        Args:
            duration_sec: Duration to record audio in seconds
            sample_rate: Audio sample rate

        Returns:
            str: Transcribed text from the captured audio
        """
        try:
            self.logger.info("🎤 Capturing real user audio during interrupt...")

            if SOUNDDEVICE_AVAILABLE:
                # Record audio using sounddevice (your old approach)
                audio_data = sd.rec(int(duration_sec * sample_rate),
                                  samplerate=sample_rate,
                                  channels=1,
                                  dtype='int16')
                sd.wait()  # Wait until recording is finished

                self.logger.info(f"✅ Audio captured ({duration_sec}s)")

                # Save to temporary WAV file for STT processing
                import tempfile
                import wave

                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                    temp_audio_path = temp_file.name

                # Write WAV file
                with wave.open(temp_audio_path, 'wb') as wf:
                    wf.setnchannels(1)  # Mono
                    wf.setsampwidth(2)  # 16-bit
                    wf.setframerate(sample_rate)
                    wf.writeframes(audio_data.tobytes())

                self.logger.info("🔄 Audio saved, transcribing...")

                # Transcribe the captured audio using STT agent
                transcribed_text = await self._transcribe_audio_file(temp_audio_path)
                self.logger.info(f"✅ Transcription result: '{transcribed_text}'")

                # Clean up temporary file
                try:
                    import os
                    os.unlink(temp_audio_path)
                except:
                    pass  # Ignore cleanup errors

                return transcribed_text

            else:
                self.logger.warning("sounddevice not available - cannot capture real audio")
                return "[Audio capture not available]"

        except Exception as e:
            self.logger.error(f"Audio capture failed: {e}")
            return f"[Audio capture error: {str(e)}]"

    async def _transcribe_audio_file(self, audio_file_path: str) -> str:
        """
        Transcribe an audio file using the STT agent.
        Based on your old working approach!

        Args:
            audio_file_path: Path to the audio file to transcribe

        Returns:
            str: Transcribed text from the audio file
        """
        try:
            from agents.stt.stt_agent import STTAgent

            self.logger.info("🔄 Using STT agent to transcribe interrupt audio")

            # Create STT agent
            stt_agent = STTAgent(session_id=self.session_id)

            # Call STT agent
            stt_result = await stt_agent.speech_to_text(audio_file_path)

            if stt_result.status == StatusType.SUCCESS:
                transcribed_text = stt_result.outputs.get('text', '').strip()
                if transcribed_text:
                    self.logger.info(f"✅ STT transcription successful: '{transcribed_text}'")
                    return transcribed_text
                else:
                    self.logger.warning("⚠️ STT returned empty transcription")
                    return "[No speech detected in interrupt audio]"
            else:
                self.logger.error(f"❌ STT agent failed: {stt_result.message}")
                return f"[STT error: {stt_result.message}]"

        except Exception as e:
            self.logger.error(f"Audio transcription failed: {e}")
            return f"[Transcription error: {str(e)}]"



    async def _resume_tts_playback(self, resume_position: float):
        """Step 5: Resume TTS from exact pause position"""
        try:
            if self.is_paused and PYGAME_AVAILABLE:
                pygame.mixer.music.unpause()
                
                # Update pause duration tracking
                if self.pause_time:
                    self.total_pause_duration += time.time() - self.pause_time
                    self.pause_time = None
                
                self.is_paused = False
                
                self.logger.info(f"TTS playback resumed from position: {resume_position:.2f}s")
                
        except Exception as e:
            self.logger.error(f"Error resuming TTS playback: {e}")

    def _check_action_reversibility(self, workflow_context: Dict[str, Any]) -> tuple:
        """
        Step 6: Check if current action is reversible using ActionReversibilityDetector
        """
        try:
            metadata = self.reversibility_detector.get_action_metadata(workflow_context)
            is_reversible = metadata.reversibility.value != "irreversible"
            level = metadata.reversibility.value

            self.logger.info(
                f"Action reversibility check: {is_reversible}",
                action="_check_action_reversibility",
                output_data={"reversible": is_reversible, "level": level},
                layer="interrupt_handler"
            )

            return is_reversible, level
            
        except Exception as e:
            self.logger.error(f"Error checking action reversibility: {e}")
            return True, "unknown"  # Default to reversible for safety

    async def _handle_conditional_queuing(self, action_reversible: bool) -> bool:
        """
        Step 7: Conditional queuing based on action reversibility
        
        Args:
            action_reversible: Whether the current action is reversible
            
        Returns:
            bool: True if user input was queued, False otherwise
        """
        try:
            if action_reversible:
                # Queue the user input for processing after TTS completion
                await self.memory_manager.set("contextual", "queued_user_input", self.user_interrupt_input)

                # Set the flag that State Manager is looking for
                await self.memory_manager.set("contextual", "has_queued_input", True)

                self.logger.info(
                    "User input queued for processing (reversible action)",
                    action="_handle_conditional_queuing",
                    input_data={"user_input": self.user_interrupt_input},
                    layer="interrupt_handler"
                )

                return True
            else:
                # Do not queue input for irreversible actions
                self.logger.info(
                    "User input NOT queued (irreversible action completed)",
                    action="_handle_conditional_queuing",
                    input_data={"user_input": self.user_interrupt_input},
                    layer="interrupt_handler"
                )
                
                return False
                
        except Exception as e:
            self.logger.error(f"Error in conditional queuing: {e}")
            return False

    async def _execute_complete_workflow_for_queued_input(self, state_manager, enhanced_input: str):
        """
        Execute the complete StateManager workflow for queued input.
        This simulates a new user input going through the complete pipeline:
        STT (skipped, we have transcript) -> Preprocessing -> Processing -> TTS

        Args:
            state_manager: Reference to the StateManager
            enhanced_input: The enhanced user input with context
        """
        try:
            self.logger.info(
                "Starting complete workflow execution for queued input",
                action="_execute_complete_workflow_for_queued_input",
                input_data={"enhanced_input": enhanced_input},
                layer="interrupt_handler"
            )

            # Step 1: Preprocessing to extract intent
            await state_manager.transitionPipeline("preprocessing")
            self.logger.info("Step 1: Preprocessing - Extracting intent...")

            preprocessing_result = await state_manager.executePipelineState({"transcript": enhanced_input})
            self.logger.info(f"Preprocessing result status: {preprocessing_result.status.value}")

            if preprocessing_result.status.value != "success":
                self.logger.error(
                    f"Preprocessing failed for queued input: {preprocessing_result.message}",
                    action="_execute_complete_workflow_for_queued_input",
                    layer="interrupt_handler"
                )
                return

            # Extract preprocessing outputs
            intent = preprocessing_result.outputs.get("intent", "unknown")
            clean_text = preprocessing_result.outputs.get("clean_text", enhanced_input)
            emotion = preprocessing_result.outputs.get("emotion") or "neutral"
            gender = preprocessing_result.outputs.get("gender") or "female"

            self.logger.info(
                "Preprocessing completed successfully",
                action="_execute_complete_workflow_for_queued_input",
                output_data={
                    "intent": intent,
                    "clean_text": clean_text,
                    "emotion": emotion,
                    "gender": gender
                },
                layer="interrupt_handler"
            )

            # Step 2: Determine target state based on intent (if StateManager supports it)
            if hasattr(state_manager, '_get_target_state_for_intent'):
                target_state = state_manager._get_target_state_for_intent(intent)
                if target_state and target_state != state_manager.current_workflow_state_id:
                    self.logger.info(f"Step 2: Transitioning to target state: {target_state}")
                    await state_manager.transitionWorkflow(target_state)

            # Step 3: Execute processing
            await state_manager.transitionPipeline("processing")
            self.logger.info("Step 3: Processing - Executing processing state...")

            processing_result = await state_manager.executePipelineState({
                "clean_text": clean_text,
                "intent": intent,
                "emotion": emotion,
                "gender": gender
            })

            if processing_result.status.value != "success":
                self.logger.error(
                    f"Processing failed for queued input: {processing_result.message}",
                    action="_execute_complete_workflow_for_queued_input",
                    layer="interrupt_handler"
                )
                return

            # Extract processing outputs
            ai_response = processing_result.outputs.get("llm_answer", "I'm sorry, I couldn't process your request.")

            self.logger.info(
                "Processing completed successfully",
                action="_execute_complete_workflow_for_queued_input",
                output_data={"ai_response": ai_response},
                layer="interrupt_handler"
            )

            # Step 4: Execute TTS
            await state_manager.transitionPipeline("tts")
            self.logger.info("Step 4: TTS - Generating response audio...")

            # Execute TTS with normal interrupt monitoring enabled for queued input
            # This ensures audio playback happens and new interrupts can be detected
            tts_result = await state_manager.executePipelineState({
                "text": ai_response,
                "emotion": emotion,
                "gender": gender
            })

            if tts_result.status.value == "success":
                self.logger.info(
                    "Complete workflow executed successfully for queued input",
                    action="_execute_complete_workflow_for_queued_input",
                    output_data={"tts_audio_path": tts_result.outputs.get("audio_path")},
                    layer="interrupt_handler"
                )

            else:
                self.logger.error(
                    f"TTS failed for queued input: {tts_result.message}",
                    action="_execute_complete_workflow_for_queued_input",
                    layer="interrupt_handler"
                )

        except Exception as e:
            self.logger.error(
                "Error executing complete workflow for queued input",
                action="_execute_complete_workflow_for_queued_input",
                reason=str(e),
                layer="interrupt_handler"
            )
            return False
